import functools
from abc import ABC, abstractmethod
from collections.abc import Awaitable, Callable
from typing import Any, TypeVar

from app.common.helpers.logger import get_logger
from app.integrations.base.credentials_resolver import ICredentials

logger = get_logger()

T = TypeVar("T")


class ITokenRefreshStrategy(ABC):
    @abstractmethod
    def should_refresh(self, exception: Exception) -> bool:
        pass

    def should_refresh_proactively(self, credentials: ICredentials) -> bool:
        return False

    @abstractmethod
    async def refresh_credentials(self, credentials: ICredentials) -> ICredentials:
        pass

    @abstractmethod
    async def reinitialize_client(
        self, instance: Any, credentials: ICredentials
    ) -> None:
        pass


def handle_token_expiry(
    strategy: ITokenRefreshStrategy,
) -> Callable[[Callable[..., Awaitable[T]]], Callable[..., Awaitable[T]]]:
    def decorator(method: Callable[..., Awaitable[T]]) -> Callable[..., Awaitable[T]]:
        @functools.wraps(method)
        async def wrapper(self, *args: Any, **kwargs: Any) -> T:
            if strategy.should_refresh_proactively(self.credentials):
                logger.info(f"Proactively refreshing token before {method.__name__}")
                refreshed_credentials = await strategy.refresh_credentials(
                    self.credentials
                )
                self.credentials = refreshed_credentials
                await strategy.reinitialize_client(self, refreshed_credentials)

            try:
                return await method(self, *args, **kwargs)
            except Exception as e:
                current_exception: BaseException | None = e
                while current_exception is not None:
                    if strategy.should_refresh(current_exception):
                        logger.warning(
                            f"Token expired during {method.__name__}, refreshing token"
                        )
                        refreshed_credentials = await strategy.refresh_credentials(
                            self.credentials
                        )
                        self.credentials = refreshed_credentials
                        await strategy.reinitialize_client(self, refreshed_credentials)
                        return await method(self, *args, **kwargs)

                    current_exception = current_exception.__cause__

                raise

        return wrapper

    return decorator
