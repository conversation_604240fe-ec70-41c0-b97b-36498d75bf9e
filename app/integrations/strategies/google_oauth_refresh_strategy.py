from datetime import UTC, datetime
from typing import Any

from google.auth.exceptions import RefreshError
from googleapiclient.errors import HttpError

from app.common.helpers.logger import get_logger
from app.integrations.adapters.google_calendar.client import GoogleCalendarClient
from app.integrations.base.credentials_resolver import ICredentials
from app.integrations.base.token_refresh_strategy import ITokenRefreshStrategy

logger = get_logger()


class GoogleOAuthRefreshStrategy(ITokenRefreshStrategy):
    def should_refresh(self, exception: Exception) -> bool:
        if isinstance(exception, HttpError):
            return exception.resp.status in [401, 403]

        if isinstance(exception, RefreshError):
            return True

        return False

    def should_refresh_proactively(self, credentials: ICredentials) -> bool:
        try:
            expires_at_str = credentials.secrets.get("expires_at")
            logger.debug(f"Checking token expiry. expires_at from DB: {expires_at_str}")
            if not expires_at_str:
                logger.debug(
                    "No expires_at found in credentials, will not refresh proactively"
                )
                return False

            if isinstance(expires_at_str, str):
                expires_at = datetime.fromisoformat(
                    expires_at_str.replace("Z", "+00:00")
                )
            else:
                expires_at = expires_at_str

            if expires_at.tzinfo is None:
                expires_at = expires_at.replace(tzinfo=UTC)

            now = datetime.now(UTC)
            time_until_expiry = expires_at - now

            logger.debug(
                f"Current time: {now}, Token expires at: {expires_at}, Time until expiry: {time_until_expiry.total_seconds():.0f} seconds"
            )

            if time_until_expiry.total_seconds() <= 0:
                logger.info("Token has already expired, will refresh immediately")
                return True
            elif time_until_expiry.total_seconds() < 300:  # 5 minutes
                logger.info(
                    f"Token expires in {time_until_expiry.total_seconds():.0f} seconds, will refresh proactively"
                )
                return True
            else:
                logger.debug(
                    f"Token is still valid for {time_until_expiry.total_seconds():.0f} seconds, no refresh needed"
                )

            return False

        except Exception as e:
            logger.warning(
                f"Could not check token expiry, will not refresh proactively: {e}"
            )
            return False

    async def refresh_credentials(self, credentials: ICredentials) -> ICredentials:
        logger.info("Refreshing Google OAuth token")
        refreshed_credentials = await credentials.refresh_token()
        logger.info("Google OAuth token refreshed successfully")
        return refreshed_credentials

    async def reinitialize_client(
        self, instance: Any, credentials: ICredentials
    ) -> None:
        if hasattr(instance, "_client") and isinstance(
            instance._client, GoogleCalendarClient
        ):
            logger.info("Re-initializing Google Calendar client with refreshed token")
            instance._client = GoogleCalendarClient(credentials=credentials.secrets)
        else:
            logger.warning(
                "Instance does not have a Google Calendar client to reinitialize"
            )
