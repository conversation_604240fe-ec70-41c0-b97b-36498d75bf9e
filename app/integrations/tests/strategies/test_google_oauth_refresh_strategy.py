from unittest.mock import AsyncMock, <PERSON><PERSON>

import pytest
from google.auth.exceptions import RefreshError
from googleapiclient.errors import HttpError

from app.integrations.adapters.google_calendar.client import GoogleCalendarClient
from app.integrations.strategies.google_oauth_refresh_strategy import (
    GoogleOAuthRefreshStrategy,
)


class TestGoogleOAuthRefreshStrategy:
    def test_should_refresh_http_error_401(self):
        """Test that 401 HTTP errors trigger refresh."""
        strategy = GoogleOAuthRefreshStrategy()

        mock_response = Mock()
        mock_response.status = 401
        error = HttpError(mock_response, b"Unauthorized")

        assert strategy.should_refresh(error) is True

    def test_should_refresh_http_error_403(self):
        """Test that 403 HTTP errors trigger refresh."""
        strategy = GoogleOAuthRefreshStrategy()

        mock_response = Mock()
        mock_response.status = 403
        error = HttpError(mock_response, b"Forbidden")

        assert strategy.should_refresh(error) is True

    def test_should_refresh_http_error_other_status(self):
        """Test that other HTTP errors don't trigger refresh."""
        strategy = GoogleOAuthRefreshStrategy()

        mock_response = Mock()
        mock_response.status = 500
        error = HttpError(mock_response, b"Server Error")

        assert strategy.should_refresh(error) is False

    def test_should_refresh_refresh_error(self):
        """Test that RefreshError triggers refresh."""
        strategy = GoogleOAuthRefreshStrategy()
        error = RefreshError("Token refresh failed")

        assert strategy.should_refresh(error) is True

    def test_should_refresh_other_exception(self):
        """Test that other exceptions don't trigger refresh."""
        strategy = GoogleOAuthRefreshStrategy()
        error = ValueError("Some other error")

        assert strategy.should_refresh(error) is False

    @pytest.mark.anyio
    async def test_refresh_credentials(self):
        """Test that credentials are refreshed correctly."""
        strategy = GoogleOAuthRefreshStrategy()

        mock_credentials = Mock()
        refreshed_credentials = Mock()
        mock_credentials.refresh_token = AsyncMock(return_value=refreshed_credentials)

        result = await strategy.refresh_credentials(mock_credentials)

        mock_credentials.refresh_token.assert_called_once()
        assert result == refreshed_credentials

    @pytest.mark.anyio
    async def test_reinitialize_client_with_google_calendar_client(self):
        """Test that Google Calendar client is reinitialized correctly."""
        strategy = GoogleOAuthRefreshStrategy()

        mock_instance = Mock()
        mock_client = Mock(spec=GoogleCalendarClient)
        mock_instance._client = mock_client

        mock_credentials = Mock()
        mock_credentials.secrets = {"access_token": "new_token"}

        await strategy.reinitialize_client(mock_instance, mock_credentials)

        # Should create a new GoogleCalendarClient instance
        assert isinstance(mock_instance._client, GoogleCalendarClient)

    @pytest.mark.anyio
    async def test_reinitialize_client_without_client(self):
        """Test that reinitialize handles instances without Google Calendar client."""
        strategy = GoogleOAuthRefreshStrategy()

        mock_instance = Mock()
        # No _client attribute
        del mock_instance._client

        mock_credentials = Mock()

        # Should not raise an exception
        await strategy.reinitialize_client(mock_instance, mock_credentials)
